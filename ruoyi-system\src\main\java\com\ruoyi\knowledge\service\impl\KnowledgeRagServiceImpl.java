package com.ruoyi.knowledge.service.impl;

import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.service.IKnowledgeBaseService;
import com.ruoyi.knowledge.service.IKnowledgeDocumentService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;
import com.ruoyi.knowledge.service.IImageOcrService;
import com.ruoyi.knowledge.service.IKnowledgeBaseStrategyConfigService;
import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import com.ruoyi.knowledge.strategy.tagging.TaggingStrategy;
import com.ruoyi.knowledge.strategy.tagging.TaggingContext;
import com.ruoyi.knowledge.strategy.tagging.TaggingResult;
import com.ruoyi.knowledge.store.CustomMilvusEmbeddingStore;
import com.ruoyi.knowledge.util.MilvusDiagnosticUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.beans.factory.annotation.Value;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.loader.FileSystemDocumentLoader;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库RAG服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class KnowledgeRagServiceImpl implements IKnowledgeRagService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeRagServiceImpl.class);

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    @Autowired(required = false)
    private CustomMilvusEmbeddingStore customMilvusEmbeddingStore;

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private DocumentSplitter documentSplitter;

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private IKnowledgeDocumentService knowledgeDocumentService;

    @Autowired
    private IImageOcrService imageOcrService;

    @Autowired
    private IKnowledgeBaseStrategyConfigService knowledgeBaseStrategyConfigService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    private MilvusDiagnosticUtil milvusDiagnosticUtil;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    /**
     * 创建知识库
     */
    @Override
    @Transactional
    public boolean createKnowledgeBase(String knowledgeBaseName, String knowledgeBaseDescription,
            List<Long> documentIds) {
        try {
            logger.info("开始创建知识库: {}, 文档数量: {}", knowledgeBaseName, documentIds.size());

            // 1. 创建知识库记录
            KnowledgeBase knowledgeBase = new KnowledgeBase();
            knowledgeBase.setName(knowledgeBaseName);
            knowledgeBase.setDescription(knowledgeBaseDescription);
            knowledgeBase.setType("rag");
            knowledgeBase.setStatus("0");
            knowledgeBase.setDocumentCount((long) documentIds.size());

            // 保存知识库
            int result = knowledgeBaseService.insertKnowledgeBase(knowledgeBase);
            if (result <= 0) {
                logger.error("创建知识库记录失败");
                return false;
            }

            Long knowledgeBaseId = knowledgeBase.getId();
            logger.info("知识库记录创建成功，ID: {}", knowledgeBaseId);

            // 2. 处理每个文档
            int successCount = 0;
            for (Long documentId : documentIds) {
                try {
                    KnowledgeDocument document = knowledgeDocumentService.selectKnowledgeDocumentById(documentId);
                    if (document != null) {
                        boolean success = addDocumentToKnowledgeBase(knowledgeBaseId, document);
                        if (success) {
                            successCount++;
                            // 更新文档的知识库ID
                            document.setKnowledgeBaseId(knowledgeBaseId);
                            knowledgeDocumentService.updateKnowledgeDocument(document);
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理文档ID {} 时出错: {}", documentId, e.getMessage(), e);
                }
            }

            logger.info("知识库创建完成，成功处理文档数: {}/{}", successCount, documentIds.size());
            return successCount > 0;

        } catch (Exception e) {
            logger.error("创建知识库失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建知识库并返回知识库ID
     */
    @Override
    @Transactional
    public Long createKnowledgeBaseWithId(String knowledgeBaseName, String knowledgeBaseDescription,
            List<Long> documentIds) {
        try {
            logger.info("开始创建知识库: {}, 文档数量: {}", knowledgeBaseName, documentIds.size());

            // 1. 创建知识库记录
            KnowledgeBase knowledgeBase = new KnowledgeBase();
            knowledgeBase.setName(knowledgeBaseName);
            knowledgeBase.setDescription(knowledgeBaseDescription);
            knowledgeBase.setType("rag");
            knowledgeBase.setStatus("0");
            knowledgeBase.setDocumentCount((long) documentIds.size());

            // 保存知识库
            int result = knowledgeBaseService.insertKnowledgeBase(knowledgeBase);
            if (result <= 0) {
                logger.error("创建知识库记录失败");
                return null;
            }

            Long knowledgeBaseId = knowledgeBase.getId();
            logger.info("知识库记录创建成功，ID: {}", knowledgeBaseId);

            // 2. 处理每个文档
            int successCount = 0;
            for (Long documentId : documentIds) {
                try {
                    KnowledgeDocument document = knowledgeDocumentService.selectKnowledgeDocumentById(documentId);
                    if (document != null) {
                        boolean success = addDocumentToKnowledgeBase(knowledgeBaseId, document);
                        if (success) {
                            successCount++;
                            // 更新文档的知识库ID
                            document.setKnowledgeBaseId(knowledgeBaseId);
                            knowledgeDocumentService.updateKnowledgeDocument(document);
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理文档ID {} 时出错: {}", documentId, e.getMessage(), e);
                }
            }

            logger.info("知识库创建完成，成功处理文档数: {}/{}", successCount, documentIds.size());
            return successCount > 0 ? knowledgeBaseId : null;

        } catch (Exception e) {
            logger.error("创建知识库失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加文档到知识库
     */
    @Override
    public boolean addDocumentToKnowledgeBase(Long knowledgeBaseId, KnowledgeDocument document) {
        try {
            logger.info("添加文档到知识库: {} -> {}", document.getName(), knowledgeBaseId);

            // 1. 检查文件是否存在
            String filePath = document.getFilePath();
            logger.info("文档文件路径: {}", filePath);

            if (filePath == null || filePath.trim().isEmpty()) {
                logger.error("文档文件路径为空: {}", document.getName());
                return false;
            }

            File file = null;

            // 首先尝试直接路径
            File directFile = new File(filePath);
            logger.info("检查直接路径: {} -> {}", filePath, directFile.exists());

            if (directFile.exists()) {
                file = directFile;
                logger.info("使用直接路径: {}", file.getAbsolutePath());
            } else {
                // 直接使用固定路径
                String basePath = "D:/RuoYi-Vue/uploadPath";
                String uploadPath = basePath + "/upload";
                logger.info("使用固定基础路径: {}", basePath);
                logger.info("上传路径: {}", uploadPath);

                // 处理路径分隔符
                String relativePath = filePath;
                if (relativePath.startsWith("/")) {
                    relativePath = relativePath.substring(1);
                }

                // 尝试多种路径组合
                String[] possiblePaths = {
                        uploadPath + File.separator + relativePath,
                        basePath + File.separator + relativePath,
                        uploadPath + File.separator + relativePath.replace("profile/upload/", ""),
                        basePath + File.separator + "upload" + File.separator
                                + relativePath.replace("profile/upload/", ""),
                        // 直接在基础路径下查找
                        basePath + File.separator + "upload" + File.separator + relativePath,
                        // 处理可能的路径变体
                        "D:/RuoYi-Vue/uploadPath/upload/" + relativePath,
                        "D:/RuoYi-Vue/uploadPath/" + relativePath
                };

                for (String possiblePath : possiblePaths) {
                    File testFile = new File(possiblePath);
                    logger.info("尝试路径: {} -> {}", testFile.getAbsolutePath(), testFile.exists());

                    if (testFile.exists()) {
                        file = testFile;
                        logger.info("找到文件，使用路径: {}", file.getAbsolutePath());
                        break;
                    }
                }

                if (file == null) {
                    logger.error("所有路径都无法找到文件: {}", filePath);
                    logger.error("尝试的路径包括:");
                    for (String path : possiblePaths) {
                        logger.error("  - {}", path);
                    }
                    return false;
                }
            }

            // 2. 根据文档类型进行不同处理
            List<TextSegment> segments;
            Document langchainDocument; // 声明在外部作用域
            String documentText; // 用于标签策略的文本内容

            // 检查是否为图片文件
            if ("image".equals(document.getType())) {
                logger.info("检测到图片文件，使用OCR提取文本: {}", document.getName());

                // 检查OCR服务是否可用
                if (!imageOcrService.isOcrAvailable()) {
                    logger.error("OCR服务不可用，无法处理图片文件");
                    return false;
                }

                // 使用OCR从图片中提取文本
                String extractedText = imageOcrService.extractTextFromImage(file);
                logger.info("从图片中提取的文本长度: {} 字符", extractedText.length());

                if (extractedText.isEmpty()) {
                    logger.warn("从图片中未能提取到文本内容，将使用图片文件名和基本信息作为文本");
                    // 使用图片的基本信息作为文本内容
                    extractedText = "图片文件: " + document.getName() + "\n" +
                            "格式: " + document.getFormat() + "\n" +
                            "大小: " + document.getSize() + " 字节\n" +
                            "上传时间: " + document.getCreateTime();
                }

                // 创建文档对象
                dev.langchain4j.data.document.Metadata metadata = new dev.langchain4j.data.document.Metadata();
                metadata.put("source", file.getAbsolutePath());
                metadata.put("type", "image");
                metadata.put("format", document.getFormat());

                langchainDocument = new Document(extractedText, metadata);
                documentText = extractedText;

                // 分割文档
                segments = documentSplitter.split(langchainDocument);
                logger.info("图片文本分割成 {} 个段落", segments.size());

            } else {
                // 非图片文件，使用标准文档加载器
                logger.info("处理标准文档文件: {}", document.getName());
                langchainDocument = FileSystemDocumentLoader.loadDocument(file.toPath());
                documentText = langchainDocument.text();

                // 分割文档
                segments = documentSplitter.split(langchainDocument);
                logger.info("文档 {} 分割成 {} 个段落", document.getName(), segments.size());
            }

            // 4. 为每个段落添加元数据
            for (TextSegment segment : segments) {
                segment.metadata().put("knowledgeBaseId", knowledgeBaseId.toString());
                segment.metadata().put("documentId", document.getId().toString());
                segment.metadata().put("documentName", document.getName());
                segment.metadata().put("documentType", document.getFormat());
            }

            // 5. 计算嵌入向量并存储
            for (TextSegment segment : segments) {
                embeddingStore.add(embeddingModel.embed(segment).content(), segment);
            }
            logger.info("文档 {} 成功添加到向量数据库", document.getName());

            // 6. 执行标签策略
            executeTaggingStrategy(knowledgeBaseId, document, documentText);

            return true;

        } catch (Exception e) {
            logger.error("添加文档到知识库失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从知识库中删除文档
     */
    @Override
    public boolean removeDocumentFromKnowledgeBase(Long knowledgeBaseId, Long documentId) {
        try {
            logger.info("从知识库 {} 中删除文档 {}", knowledgeBaseId, documentId);

            // 注意：RedisEmbeddingStore 可能不支持按元数据删除
            // 这里需要根据实际的存储实现来调整
            logger.warn("当前实现不支持从向量数据库中删除特定文档，请手动清理");

            return true;
        } catch (Exception e) {
            logger.error("从知识库中删除文档失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 在知识库中搜索相关内容
     */
    @Override
    public List<String> searchInKnowledgeBase(Long knowledgeBaseId, String query, int maxResults) {
        try {
            logger.info("在知识库 {} 中搜索: {}, 存储类型: {}", knowledgeBaseId, query, storeType);

            // 1. 计算查询的嵌入向量
            logger.info("开始计算查询嵌入向量...");

            List<EmbeddingMatch<TextSegment>> matches;

            // 根据存储类型选择不同的搜索策略
            if ("milvus".equalsIgnoreCase(storeType) && customMilvusEmbeddingStore != null) {
                // 使用Milvus时，直接在指定知识库的集合中搜索
                logger.info("使用Milvus存储，在知识库 {} 的专用集合中搜索", knowledgeBaseId);
                matches = customMilvusEmbeddingStore.searchInKnowledgeBase(knowledgeBaseId,
                        dev.langchain4j.store.embedding.EmbeddingSearchRequest.builder()
                                .queryEmbedding(embeddingModel.embed(query).content())
                                .maxResults(maxResults)
                                .minScore(0.6)
                                .build())
                        .matches();
            } else {
                // 使用内存存储或Redis时，在全局存储中搜索然后过滤
                logger.info("使用 {} 存储，在全局存储中搜索后过滤", storeType);
                matches = embeddingStore.search(
                        dev.langchain4j.store.embedding.EmbeddingSearchRequest.builder()
                                .queryEmbedding(embeddingModel.embed(query).content())
                                .maxResults(maxResults * 3) // 增加搜索数量以便过滤
                                .minScore(0.6)
                                .build())
                        .matches();
            }

            logger.info("向量搜索完成，找到 {} 个匹配项", matches.size());

            // 打印所有匹配项的详细信息
            for (int i = 0; i < matches.size(); i++) {
                EmbeddingMatch<TextSegment> match = matches.get(i);
                String segmentKnowledgeBaseId = match.embedded().metadata().getString("knowledgeBaseId");
                String documentName = match.embedded().metadata().getString("documentName");
                double score = match.score();
                String text = match.embedded().text();

                logger.info("匹配项 {}: 知识库ID={}, 文档名={}, 相似度={}, 文本长度={}, 文本预览={}",
                        i + 1, segmentKnowledgeBaseId, documentName, score, text.length(),
                        text.length() > 50 ? text.substring(0, 50) + "..." : text);
            }

            List<String> results;

            if ("milvus".equalsIgnoreCase(storeType) && customMilvusEmbeddingStore != null) {
                // Milvus已经在特定集合中搜索，无需过滤
                results = matches.stream()
                        .limit(maxResults) // 限制结果数量
                        .map(match -> match.embedded().text())
                        .collect(Collectors.toList());
            } else {
                // 2. 过滤属于指定知识库的结果
                results = matches.stream()
                        .filter(match -> {
                            String segmentKnowledgeBaseId = match.embedded().metadata().getString("knowledgeBaseId");
                            boolean matches_kb = knowledgeBaseId.toString().equals(segmentKnowledgeBaseId);
                            if (!matches_kb) {
                                logger.debug("过滤掉不匹配的知识库: 期望={}, 实际={}", knowledgeBaseId, segmentKnowledgeBaseId);
                            }
                            return matches_kb;
                        })
                        .limit(maxResults) // 限制结果数量
                        .map(match -> match.embedded().text())
                        .collect(Collectors.toList());
            }

            logger.info("搜索完成，最终找到 {} 个相关结果", results.size());
            return results;

        } catch (Exception e) {
            logger.error("在知识库中搜索失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查知识库是否存在
     */
    @Override
    public boolean knowledgeBaseExists(Long knowledgeBaseId) {
        try {
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            return knowledgeBase != null;
        } catch (Exception e) {
            logger.error("检查知识库是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取知识库中的文档数量
     */
    @Override
    public long getDocumentCount(Long knowledgeBaseId) {
        try {
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            if (knowledgeBase != null) {
                Long documentCount = knowledgeBase.getDocumentCount();
                logger.info("知识库 {} 的文档数量: {}", knowledgeBaseId, documentCount);
                return documentCount != null ? documentCount : 0;
            } else {
                logger.warn("知识库 {} 不存在", knowledgeBaseId);
                return 0;
            }
        } catch (Exception e) {
            logger.error("获取知识库文档数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 执行标签策略
     */
    private void executeTaggingStrategy(Long knowledgeBaseId, KnowledgeDocument document, String content) {
        try {
            // 查询知识库的标签策略配置
            KnowledgeBaseStrategyConfig queryConfig = new KnowledgeBaseStrategyConfig();
            queryConfig.setKnowledgeBaseId(knowledgeBaseId);
            queryConfig.setStrategyType("TAGGING");
            queryConfig.setIsEnabled("1");

            List<KnowledgeBaseStrategyConfig> taggingConfigs = knowledgeBaseStrategyConfigService
                    .selectKnowledgeBaseStrategyConfigList(queryConfig);

            if (taggingConfigs.isEmpty()) {
                logger.info("知识库 {} 未配置标签策略", knowledgeBaseId);
                return;
            }

            // 执行第一个启用的标签策略
            KnowledgeBaseStrategyConfig config = taggingConfigs.get(0);
            logger.info("执行标签策略: 知识库ID={}, 策略模板ID={}", knowledgeBaseId, config.getStrategyTemplateId());

            // 获取标签策略实现
            TaggingStrategy taggingStrategy = applicationContext.getBean("defaultTaggingStrategy", TaggingStrategy.class);

            // 构建标签上下文
            TaggingContext context = new TaggingContext(knowledgeBaseId, document, content);

            // 执行标签策略
            TaggingResult result = taggingStrategy.execute(context, config.getConfigJson());

            if (result.isSuccess() && !result.getTags().isEmpty()) {
                // 更新文档标签
                String tags = result.getTagsAsString();
                document.setTags(tags);
                knowledgeDocumentService.updateKnowledgeDocument(document);

                logger.info("文档 {} 标签生成成功: {}", document.getName(), tags);
            } else {
                logger.warn("文档 {} 标签生成失败: {}", document.getName(), result.getMessage());
            }

        } catch (Exception e) {
            logger.error("执行标签策略失败: 知识库ID={}, 文档={}", knowledgeBaseId, document.getName(), e);
        }
    }

    /**
     * 诊断Milvus连接和配置
     */
    public void diagnoseMilvus() {
        logger.info("开始诊断Milvus配置和连接状态...");

        if (milvusDiagnosticUtil != null) {
            milvusDiagnosticUtil.diagnose();
        } else {
            logger.warn("MilvusDiagnosticUtil未注入，无法执行诊断");
        }
    }
}
