# Milvus知识库搜索修复指南

## 问题描述

用户反馈：知识库构建使用memory进行存储时，AI问答可以正确调用知识库并回答问题，但是使用milvus进行存储时AI却无法正确调用，后端日志显示知识库未查询到结果。

## 问题分析

经过代码分析，发现了以下关键问题：

### 1. 搜索方法使用错误
在 `KnowledgeRagServiceImpl.searchInKnowledgeBase()` 方法中，代码使用的是通用的 `embeddingStore.search()` 方法，而不是针对特定知识库的搜索。

**问题代码：**
```java
List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(
    dev.langchain4j.store.embedding.EmbeddingSearchRequest.builder()
        .queryEmbedding(embeddingModel.embed(query).content())
        .maxResults(maxResults)
        .minScore(0.6)
        .build())
    .matches();
```

### 2. Milvus URI配置错误
在 `MilvusConfig.getUri()` 方法中，使用了HTTP/HTTPS协议，但Milvus实际使用的是gRPC协议。

**问题代码：**
```java
public String getUri() {
    String protocol = secure ? "https" : "http";
    return String.format("%s://%s:%d", protocol, host, port);
}
```

### 3. 缺少针对Milvus的特殊处理
当使用Milvus存储时，应该调用 `CustomMilvusEmbeddingStore.searchInKnowledgeBase()` 方法来在特定知识库的集合中搜索，而不是在全局存储中搜索后过滤。

## 修复方案

### 1. 修复搜索逻辑

修改 `KnowledgeRagServiceImpl.searchInKnowledgeBase()` 方法，根据存储类型选择不同的搜索策略：

```java
// 根据存储类型选择不同的搜索策略
if ("milvus".equalsIgnoreCase(storeType) && customMilvusEmbeddingStore != null) {
    // 使用Milvus时，直接在指定知识库的集合中搜索
    matches = customMilvusEmbeddingStore.searchInKnowledgeBase(knowledgeBaseId,
            dev.langchain4j.store.embedding.EmbeddingSearchRequest.builder()
                    .queryEmbedding(embeddingModel.embed(query).content())
                    .maxResults(maxResults)
                    .minScore(0.6)
                    .build())
            .matches();
} else {
    // 使用内存存储或Redis时，在全局存储中搜索然后过滤
    matches = embeddingStore.search(...).matches();
}
```

### 2. 修复Milvus URI配置

```java
public String getUri() {
    // Milvus使用gRPC协议，不是HTTP
    return String.format("%s:%d", host, port);
}
```

### 3. 添加依赖注入

在 `KnowledgeRagServiceImpl` 中添加对 `CustomMilvusEmbeddingStore` 的注入：

```java
@Autowired(required = false)
private CustomMilvusEmbeddingStore customMilvusEmbeddingStore;

@Value("${knowledge.embedding.store.type:memory}")
private String storeType;
```

### 4. 增强错误处理和日志

在 `CustomMilvusEmbeddingStore.searchInKnowledgeBase()` 方法中添加详细的日志和错误处理。

## 新增功能

### 1. 诊断工具
创建了 `MilvusDiagnosticUtil` 类，用于诊断Milvus连接和配置问题：
- 检查配置是否正确
- 测试连接状态
- 提供故障排除建议

### 2. 诊断接口
创建了 `KnowledgeDiagnosticController` 控制器，提供以下接口：
- `GET /knowledge/diagnostic/milvus` - 诊断Milvus配置
- `GET /knowledge/diagnostic/search` - 测试搜索功能

### 3. 测试用例
创建了 `MilvusSearchTest` 测试类，用于验证修复效果。

## 使用方法

### 1. 启动应用后诊断
访问诊断接口来检查Milvus配置：
```
GET http://localhost:8080/knowledge/diagnostic/milvus
```

### 2. 测试搜索功能
```
GET http://localhost:8080/knowledge/diagnostic/search
```

### 3. 查看日志
修复后的代码会输出详细的调试日志，帮助定位问题：
```
2024-08-16 10:00:00 INFO  - 在知识库 1 中搜索: 测试查询, 存储类型: milvus
2024-08-16 10:00:00 INFO  - 使用Milvus存储，在知识库 1 的专用集合中搜索
2024-08-16 10:00:00 INFO  - 开始在知识库 1 中搜索，请求参数: maxResults=3, minScore=0.6
2024-08-16 10:00:00 INFO  - 在集合 knowledge_base_1 中搜索
2024-08-16 10:00:00 INFO  - 知识库 1 搜索完成，找到 2 个结果
```

## 验证修复

1. **确保Milvus服务运行正常**
2. **检查配置文件中存储类型设置为milvus**
3. **访问诊断接口验证连接**
4. **测试知识库搜索功能**
5. **查看日志确认使用了正确的搜索路径**

## 注意事项

1. 确保Milvus服务在配置的地址和端口上运行
2. 如果使用认证，确保用户名和密码正确
3. 确保知识库数据已正确导入到Milvus中
4. 检查集合名称是否符合预期格式：`knowledge_base_{知识库ID}`

## 故障排除

如果仍然遇到问题，请：
1. 查看应用启动日志中的Milvus初始化信息
2. 使用诊断接口检查配置
3. 确认Milvus服务状态
4. 检查网络连接和防火墙设置
